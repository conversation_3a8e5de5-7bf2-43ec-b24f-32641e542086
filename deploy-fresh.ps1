# Park & Rent - Fresh Deployment Script for Hostinger
# This script prepares the Laravel API for deployment at domain root

Write-Host "🎉 PARK & RENT - FRESH DEPLOYMENT PREPARATION" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Check if deployment folder exists
if (Test-Path "hostinger-deploy-fresh") {
    Write-Host "✅ Deployment folder found: hostinger-deploy-fresh/" -ForegroundColor Green
} else {
    Write-Host "❌ Deployment folder not found!" -ForegroundColor Red
    Write-Host "Please run this script from the project root directory." -ForegroundColor Yellow
    exit 1
}

# Verify key files exist
$keyFiles = @(
    "hostinger-deploy-fresh/index.php",
    "hostinger-deploy-fresh/.htaccess",
    "hostinger-deploy-fresh/.env",
    "hostinger-deploy-fresh/bootstrap/app.php",
    "hostinger-deploy-fresh/routes/api.php"
)

Write-Host "`n🔍 Verifying deployment files..." -ForegroundColor Yellow
foreach ($file in $keyFiles) {
    if (Test-Path $file) {
        Write-Host "   ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "   ❌ $file - MISSING!" -ForegroundColor Red
    }
}

# Check Laravel structure
Write-Host "`n📁 Checking Laravel structure..." -ForegroundColor Yellow
$folders = @("app", "bootstrap", "config", "database", "routes", "storage", "vendor")
foreach ($folder in $folders) {
    if (Test-Path "hostinger-deploy-fresh/$folder") {
        Write-Host "   ✅ $folder/" -ForegroundColor Green
    } else {
        Write-Host "   ❌ $folder/ - MISSING!" -ForegroundColor Red
    }
}

# Display deployment summary
Write-Host "`n📋 DEPLOYMENT SUMMARY:" -ForegroundColor Cyan
Write-Host "   🎯 Target: https://ebisera.com (domain root)" -ForegroundColor White
Write-Host "   📁 Source: hostinger-deploy-fresh/" -ForegroundColor White
Write-Host "   🗄️ Database: MySQL (update .env file)" -ForegroundColor White
Write-Host "   🔧 Framework: Laravel API with Sanctum auth" -ForegroundColor White

Write-Host "`n🚀 NEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. Upload hostinger-deploy-fresh/* to public_html/" -ForegroundColor White
Write-Host "2. Update .env with your database credentials" -ForegroundColor White
Write-Host "3. Run: php artisan migrate --force" -ForegroundColor White
Write-Host "4. Run: php artisan db:seed --force" -ForegroundColor White
Write-Host "5. Run: php artisan storage:link" -ForegroundColor White
Write-Host "6. Test: https://ebisera.com/api/cars" -ForegroundColor White

Write-Host "`n📖 See DEPLOYMENT_INSTRUCTIONS_FRESH.txt for detailed steps" -ForegroundColor Green
Write-Host "Ready for deployment!" -ForegroundColor Green
