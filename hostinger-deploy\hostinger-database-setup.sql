-- Park & Rent - Database Setup for Hostinger
-- Run these commands after uploading files and configuring database

-- 1. First, make sure your .env file is configured with correct database credentials
-- 2. Then run these commands in cPanel Terminal or SSH:

-- Navigate to API directory
-- cd public_html/api

-- Install/update Composer dependencies (if needed)
-- composer install --optimize-autoloader --no-dev

-- Clear any cached config
-- php artisan config:clear
-- php artisan cache:clear
-- php artisan route:clear
-- php artisan view:clear

-- Run migrations to create tables
-- php artisan migrate --force

-- Seed the database with initial data
-- php artisan db:seed --force

-- Create storage link (if needed)
-- php artisan storage:link

-- Set proper permissions
-- chmod -R 755 storage/
-- chmod -R 755 bootstrap/cache/

-- Optional: Create admin user manually if seeder doesn't work
-- You can run this SQL directly in phpMyAdmin if needed:

INSERT INTO users (name, email, email_verified_at, password, role, phone, created_at, updated_at) 
VALUES (
    'Admin User', 
    '<EMAIL>', 
    NOW(), 
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'admin', 
    '**********', 
    NOW(), 
    NOW()
);

-- Test accounts (optional)
INSERT INTO users (name, email, email_verified_at, password, role, phone, created_at, updated_at) 
VALUES 
(
    'Test Client', 
    '<EMAIL>', 
    NOW(), 
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'client', 
    '**********', 
    NOW(), 
    NOW()
),
(
    'Test Owner', 
    '<EMAIL>', 
    NOW(), 
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'owner', 
    '**********', 
    NOW(), 
    NOW()
);
