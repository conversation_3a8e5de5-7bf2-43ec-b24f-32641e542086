🎉 PARK & RENT API - FRESH HOSTINGER DEPLOYMENT INSTRUCTIONS
================================================================

📁 Files prepared in: hostinger-deploy-fresh/

🎯 DEPLOYMENT TYPE: Laravel API at Domain Root (https://ebisera.com)

📋 STEP-BY-STEP DEPLOYMENT:

1. 🌐 LOGIN TO HOSTINGER CPANEL
   - Go to your Hostinger control panel
   - Open File Manager

2. 📤 UPLOAD FILES
   - Upload ALL contents of 'hostinger-deploy-fresh/' to 'public_html/'
   - Make sure the structure is:
     public_html/
     ├── index.php (Laravel entry point)
     ├── .htaccess (Laravel routing)
     ├── app/ (Laravel application)
     ├── bootstrap/ (<PERSON><PERSON> bootstrap)
     ├── config/ (<PERSON>vel config)
     ├── database/ (Laravel database)
     ├── routes/ (Laravel routes)
     ├── storage/ (Laravel storage)
     ├── vendor/ (Composer dependencies)
     └── .env (production environment)

3. 🗄️ SETUP DATABASE
   - Create MySQL database in cPanel
   - Note: database name, username, password
   - Update public_html/.env with your database details:
     DB_DATABASE=your_database_name
     DB_USERNAME=your_database_username  
     DB_PASSWORD=your_database_password

4. 🔧 RUN MIGRATIONS
   - In cPanel Terminal or SSH:
     cd public_html
     php artisan migrate --force
     php artisan db:seed --force

5. 🔗 CREATE STORAGE LINK
   - In cPanel Terminal or SSH:
     cd public_html
     php artisan storage:link

6. ⚙️ OPTIMIZE FOR PRODUCTION
   - In cPanel Terminal or SSH:
     cd public_html
     php artisan config:cache
     php artisan route:cache
     php artisan view:cache

7. 🔐 SET PERMISSIONS
   - Set storage/ and bootstrap/cache/ to 755 or 775
   - Set .env file to 644

8. 🧪 TEST THE API
   - Visit: https://ebisera.com/api/cars
   - Should return JSON data of cars
   - Visit: https://ebisera.com/api/drivers  
   - Should return JSON data of drivers

📝 IMPORTANT NOTES:

✅ Laravel API will be accessible directly at https://ebisera.com
✅ All API routes are prefixed with /api/ (e.g., /api/cars, /api/login)
✅ Admin routes are at /api/admin/ (e.g., /api/admin/cars)
✅ Owner routes are at /api/owner/ (e.g., /api/owner/cars)
✅ Authentication uses Laravel Sanctum
✅ File uploads work with storage link
✅ Database uses MySQL (not SQLite)

🔧 CONFIGURATION UPDATES NEEDED:

1. Update .env file with your actual database credentials
2. Update .env file with your actual email settings
3. Generate new APP_KEY if needed: php artisan key:generate

🚀 API ENDPOINTS AVAILABLE:

Public Routes:
- GET /api/cars - List all cars
- GET /api/cars/{id} - Get car details
- GET /api/drivers - List all drivers
- GET /api/drivers/{id} - Get driver details
- POST /api/register - User registration
- POST /api/login - User login

Protected Routes (require authentication):
- POST /api/logout - User logout
- GET /api/user - Get current user
- All CRUD operations for cars, drivers, bookings, etc.

Admin Routes:
- /api/admin/* - Full admin functionality

Owner Routes:
- /api/owner/* - Owner dashboard functionality

📞 SUPPORT:
If you encounter any issues, check:
1. Database connection in .env
2. File permissions
3. Laravel logs in storage/logs/
4. PHP version compatibility (requires PHP 8.2+)

🎉 Your Park & Rent API will be live at https://ebisera.com!
