<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Car;
use App\Models\GpsInstallationRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CarController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Car::with('owner')->where('is_active', true);

        // Apply filters if provided
        if ($request->has('location')) {
            $query->where('location', 'like', '%' . $request->location . '%');
        }

        if ($request->has('min_price')) {
            $query->where('price_per_hour', '>=', $request->min_price);
        }

        if ($request->has('max_price')) {
            $query->where('price_per_hour', '<=', $request->max_price);
        }

        if ($request->has('make')) {
            $query->where('make', $request->make);
        }

        if ($request->has('model')) {
            $query->where('model', $request->model);
        }

        if ($request->has('year')) {
            $query->where('year', $request->year);
        }

        $cars = $query->get();

        return response()->json([
            'cars' => $cars
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Check if user is an owner
        if (auth()->user()->role !== 'owner' && auth()->user()->role !== 'admin') {
            return response()->json([
                'message' => 'Access denied',
                'error' => 'Only car owners and administrators can add cars to the platform.',
                'error_type' => 'unauthorized_access'
            ], 403);
        }



        $validator = Validator::make($request->all(), [
            'make' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'images' => 'sometimes|array',
            'images.*' => 'file|image|mimes:jpeg,png,jpg,gif|max:2048',
            'description' => 'required|string',
            'features' => 'required|string', // JSON string from frontend
            'location' => 'required|string|max:255',
            'price_per_hour' => 'required|numeric|min:0',
            'availability_notes' => 'nullable|string',
            'is_active' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'error' => 'Please check your input and try again.',
                'error_type' => 'validation_error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Handle image uploads
        $imagePaths = [];
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $path = $image->store('car-images', 'public');
                $imagePaths[] = url('/storage/' . $path);
            }
        }

        // Parse features JSON string
        $features = [];
        if ($request->features) {
            $features = json_decode($request->features, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return response()->json(['errors' => ['features' => ['Invalid features format']]], 422);
            }
        }

        $car = Car::create([
            'owner_id' => auth()->id(),
            'make' => $request->make,
            'model' => $request->model,
            'year' => $request->year,
            'images' => $imagePaths,
            'description' => $request->description,
            'features' => $features,
            'location' => $request->location,
            'price_per_hour' => $request->price_per_hour,
            'availability_notes' => $request->availability_notes,
            'is_active' => $request->has('is_active') ? (bool)$request->is_active : true,
        ]);

        return response()->json([
            'message' => 'Car added successfully',
            'car' => $car
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(string $id)
    {
        $car = Car::with('owner')->findOrFail($id);

        return response()->json([
            'car' => $car
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, string $id)
    {
        $car = Car::findOrFail($id);

        // Check if user is the owner of the car or an admin
        if (auth()->id() != $car->owner_id && auth()->user()->role !== 'admin') {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'make' => 'sometimes|string|max:255',
            'model' => 'sometimes|string|max:255',
            'year' => 'sometimes|integer|min:1900|max:' . (date('Y') + 1),
            'images' => 'sometimes|array',
            'images.*' => 'string',
            'description' => 'sometimes|string',
            'features' => 'sometimes|string', // JSON string from frontend
            'location' => 'sometimes|string|max:255',
            'price_per_hour' => 'sometimes|numeric|min:0',
            'availability_notes' => 'sometimes|nullable|string',
            'is_active' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Prepare data for update
        $updateData = $request->except(['features']);

        // Handle features if provided
        if ($request->has('features')) {
            $features = json_decode($request->features, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $updateData['features'] = $features;
            }
        }

        $car->update($updateData);

        return response()->json([
            'message' => 'Car updated successfully',
            'car' => $car
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(string $id)
    {
        $car = Car::findOrFail($id);

        // Check if user is the owner of the car or an admin
        if (auth()->id() != $car->owner_id && auth()->user()->role !== 'admin') {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $car->delete();

        return response()->json([
            'message' => 'Car deleted successfully'
        ]);
    }

    /**
     * Get cars owned by the authenticated user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function myCars()
    {
        $cars = Car::where('owner_id', auth()->id())->get();

        return response()->json($cars);
    }

    /**
     * Toggle the active status of a car.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function toggleActive(string $id)
    {
        $car = Car::findOrFail($id);

        // Check if user is the owner of the car or an admin
        if (auth()->id() != $car->owner_id && auth()->user()->role !== 'admin') {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $car->update([
            'is_active' => !$car->is_active
        ]);

        return response()->json([
            'message' => 'Car status updated successfully',
            'is_active' => $car->is_active
        ]);
    }

    /**
     * Request GPS installation for a car.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function requestGpsInstallation(Request $request, $id)
    {
        $car = Car::findOrFail($id);

        // Check if user is the owner of the car
        if (auth()->id() != $car->owner_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'reason' => 'required|string|max:1000',
            'contact_phone' => 'required|string|max:20',
            'preferred_installation_date' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Check if there's already a pending request for this car
        $existingRequest = GpsInstallationRequest::where('car_id', $car->id)
            ->where('status', 'pending')
            ->first();

        if ($existingRequest) {
            return response()->json([
                'message' => 'There is already a pending GPS installation request for this car'
            ], 400);
        }

        $gpsRequest = GpsInstallationRequest::create([
            'user_id' => auth()->id(),
            'car_id' => $car->id,
            'car_make' => $car->make,
            'car_model' => $car->model,
            'car_year' => $car->year,
            'license_plate' => $car->license_plate,
            'reason' => $request->reason,
            'contact_phone' => $request->contact_phone,
            'preferred_installation_date' => $request->preferred_installation_date,
            'status' => 'pending',
        ]);

        return response()->json([
            'message' => 'GPS installation request submitted successfully',
            'request' => $gpsRequest->load(['user', 'car'])
        ], 201);
    }
}
