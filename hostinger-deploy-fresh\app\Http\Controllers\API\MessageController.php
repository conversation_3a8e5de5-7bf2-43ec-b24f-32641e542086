<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Chat;
use App\Models\Message;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Notifications\NewChatMessage;

class MessageController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'chat_id' => 'required|exists:chats,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $chat = Chat::findOrFail($request->chat_id);

        // Check if user is part of the chat
        if (auth()->id() != $chat->user_id && auth()->id() != $chat->recipient_id && auth()->user()->role !== 'admin') {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $messages = Message::where('chat_id', $request->chat_id)
            ->with('sender')
            ->orderBy('created_at', 'asc')
            ->get();

        // Mark messages as read if user is the recipient
        if (auth()->id() == $chat->recipient_id) {
            Message::where('chat_id', $chat->id)
                ->where('sender_id', '!=', auth()->id())
                ->where('is_read', false)
                ->update([
                    'is_read' => true,
                    'read_at' => now(),
                ]);
        }

        return response()->json($messages);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'chat_id' => 'required|exists:chats,id',
            'content' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $chat = Chat::findOrFail($request->chat_id);

        // Check if user is part of the chat
        if (auth()->id() != $chat->user_id && auth()->id() != $chat->recipient_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $message = Message::create([
            'chat_id' => $request->chat_id,
            'sender_id' => auth()->id(),
            'content' => $request->content,
            'is_read' => false,
        ]);

        // Update last_message_at in chat
        $chat->update([
            'last_message_at' => now(),
            'is_active' => true,
        ]);

        // Send email notification to recipient
        $recipient = $chat->user_id === auth()->id() ? $chat->recipient : $chat->user;
        $recipient->notify(new NewChatMessage($message, $chat));

        return response()->json([
            'message' => 'Message sent successfully',
            'data' => $message
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(string $id)
    {
        $message = Message::with('sender')->findOrFail($id);
        $chat = Chat::findOrFail($message->chat_id);

        // Check if user is part of the chat
        if (auth()->id() != $chat->user_id && auth()->id() != $chat->recipient_id && auth()->user()->role !== 'admin') {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Mark message as read if user is the recipient and not the sender
        if (auth()->id() != $message->sender_id && !$message->is_read) {
            $message->update([
                'is_read' => true,
                'read_at' => now(),
            ]);
        }

        return response()->json($message);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, string $id)
    {
        // Messages cannot be edited after they are sent
        return response()->json(['message' => 'Method not allowed'], 405);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(string $id)
    {
        $message = Message::findOrFail($id);
        $chat = Chat::findOrFail($message->chat_id);

        // Check if user is the sender of the message or an admin
        if (auth()->id() != $message->sender_id && auth()->user()->role !== 'admin') {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Only allow deletion of messages sent within the last hour
        $oneHourAgo = now()->subHour();
        if ($message->created_at < $oneHourAgo) {
            return response()->json(['message' => 'Messages can only be deleted within one hour of sending'], 422);
        }

        $message->delete();

        return response()->json([
            'message' => 'Message deleted successfully'
        ]);
    }

    /**
     * Mark messages as read.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsRead(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'chat_id' => 'required|exists:chats,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $chat = Chat::findOrFail($request->chat_id);

        // Check if user is part of the chat
        if (auth()->id() != $chat->user_id && auth()->id() != $chat->recipient_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $count = Message::where('chat_id', $request->chat_id)
            ->where('sender_id', '!=', auth()->id())
            ->where('is_read', false)
            ->update([
                'is_read' => true,
                'read_at' => now(),
            ]);

        return response()->json([
            'message' => $count . ' messages marked as read'
        ]);
    }
}
