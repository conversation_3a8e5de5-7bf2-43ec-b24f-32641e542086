APP_NAME="Park & Rent"
APP_ENV=production
APP_KEY=base64:x+IkkbBWDRJPMrrjiTU6Xo1gkt/pW2PdeFE4u5lXhMg=
APP_DEBUG=true
APP_URL=https://ebisera.com

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=u555127771_parkandrent_db
DB_USERNAME=u555127771_parkandrent_us
DB_PASSWORD=Ebisera@2020

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=public
QUEUE_CONNECTION=database

CACHE_STORE=file
CACHE_PREFIX=

SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

MAIL_MAILER=smtp
MAIL_HOST=smtp.hostinger.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=Ebisera@2020
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Park & Rent"

# Sanctum Configuration
SANCTUM_STATEFUL_DOMAINS=ebisera.com,www.ebisera.com
SESSION_DOMAIN=.ebisera.com

# CORS Configuration
FRONTEND_URL=https://ebisera.com
