# Park & Rent - Hostinger Troubleshooting Guide

## 🔧 Common Issues and Solutions

### 1. 🚫 API Requests Failing (404 errors)

**Problem**: Frontend can't reach Laravel API
**Solution**: 
- Check `.htaccess` files are in correct locations
- Verify API folder structure: `public_html/api/public/index.php`
- Test API directly: `https://yourdomain.com/api/api/cars`

### 2. 🗄️ Database Connection Errors

**Problem**: <PERSON><PERSON> can't connect to MySQL
**Solution**:
- Verify database credentials in `public_html/api/.env`
- Check database name, username, password in Hostinger cPanel
- Ensure database user has all privileges

### 3. 📁 File Permissions Issues

**Problem**: <PERSON><PERSON> errors about storage/cache permissions
**Solution**:
```bash
chmod -R 755 public_html/api/storage/
chmod -R 755 public_html/api/bootstrap/cache/
```

### 4. 🔑 Authentication Not Working

**Problem**: Login/logout not functioning
**Solution**:
- Check `APP_KEY` is set in `.env`
- Run: `php artisan key:generate` if needed
- Verify session configuration in `.env`

### 5. 🌐 React Router Not Working

**Problem**: Direct URLs return 404
**Solution**:
- Verify root `.htaccess` is in `public_html/.htaccess`
- Check RewriteEngine is enabled on server

### 6. 📧 Email Not Sending

**Problem**: Email notifications failing
**Solution**:
- Configure MAIL settings in `.env` with Hostinger SMTP
- Use: `smtp.hostinger.com`, port 587, TLS encryption

### 7. 🖼️ Images Not Loading

**Problem**: Car images not displaying
**Solution**:
- Run: `php artisan storage:link`
- Check storage folder permissions
- Verify image upload paths

## 🔍 Debugging Steps

### Check Laravel Logs
```bash
tail -f public_html/api/storage/logs/laravel.log
```

### Test API Endpoints
- Cars: `https://yourdomain.com/api/api/cars`
- Login: `https://yourdomain.com/api/api/login`
- User: `https://yourdomain.com/api/api/user`

### Verify Database Tables
Check in phpMyAdmin that these tables exist:
- users
- cars
- drivers
- bookings
- chats
- messages

### Clear Laravel Cache
```bash
cd public_html/api
php artisan config:clear
php artisan cache:clear
php artisan route:clear
```

## 📞 Support Contacts

- **Developer**: <EMAIL>
- **Phone**: **********
- **Hostinger Support**: Available in cPanel

## 🎯 Quick Health Check

1. ✅ Frontend loads: `https://yourdomain.com`
2. ✅ API responds: `https://yourdomain.com/api/api/cars`
3. ✅ Database connected: Check Laravel logs
4. ✅ Authentication works: Try login
5. ✅ File uploads work: Try adding a car with image
