<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return response()->json([
        'message' => 'Park & Rent API',
        'version' => '1.0.0',
        'status' => 'active',
        'endpoints' => [
            'cars' => '/api/cars',
            'drivers' => '/api/drivers',
            'auth' => '/api/login',
            'register' => '/api/register'
        ]
    ]);
});

// API Status route
Route::get('/status', function () {
    return response()->json([
        'status' => 'OK',
        'timestamp' => now(),
        'laravel_version' => app()->version()
    ]);
});
